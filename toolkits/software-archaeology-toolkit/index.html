<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Software Architecture Recovery & Reverse Engineering Handbook</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-okaidia.min.css" media="(prefers-color-scheme: dark)">
</head>
<body>
    <div class="handbook-container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <h1 class="header-title">
                    <span class="title-icon">🔧</span>
                    Software Architecture Recovery & Reverse Engineering
                </h1>
                <div class="header-controls">
                    <div class="search-container">
                        <input type="text" id="search-input" class="search-input" placeholder="Tìm kiếm trong handbook...">
                        <button class="search-btn" id="search-btn">🔍</button>
                    </div>
                    <button class="theme-toggle" id="theme-toggle" title="Toggle theme">🌙</button>
                </div>
            </div>
        </header>

        <div class="main-layout">
            <!-- Sidebar -->
            <aside class="sidebar" id="sidebar">
                <nav class="sidebar-nav">
                    <ul class="nav-list">
                        <li class="nav-item">
                            <button class="nav-button active" data-section="overview">
                                <span class="nav-icon">📖</span>
                                Overview & Core Concepts
                            </button>
                        </li>
                        <li class="nav-item">
                            <button class="nav-button" data-section="linux-commands">
                                <span class="nav-icon">💻</span>
                                Linux Commands & System Analysis
                            </button>
                        </li>
                        <li class="nav-item">
                            <button class="nav-button" data-section="static-analysis">
                                <span class="nav-icon">🔍</span>
                                Static Code Analysis Tools
                            </button>
                        </li>
                        <li class="nav-item">
                            <button class="nav-button" data-section="database-analysis">
                                <span class="nav-icon">🗄️</span>
                                Database Schema Analysis
                            </button>
                        </li>
                        <li class="nav-item">
                            <button class="nav-button" data-section="documentation-generation">
                                <span class="nav-icon">📄</span>
                                Automated Documentation Generation
                            </button>
                        </li>
                        <li class="nav-item">
                            <button class="nav-button" data-section="graph-generation">
                                <span class="nav-icon">📊</span>
                                Graph Generation & Visualization
                            </button>
                        </li>
                        <li class="nav-item">
                            <button class="nav-button" data-section="comprehensive-script">
                                <span class="nav-icon">⚙️</span>
                                Comprehensive Analysis Scripts
                            </button>
                        </li>
                        <li class="nav-item">
                            <button class="nav-button" data-section="professional-tools">
                                <span class="nav-icon">🛠️</span>
                                Professional Tools
                            </button>
                        </li>
                        <li class="nav-item">
                            <button class="nav-button" data-section="workflows">
                                <span class="nav-icon">📋</span>
                                Workflows & Best Practices
                            </button>
                        </li>
                    </ul>
                </nav>
            </aside>

            <!-- Main Content -->
            <main class="main-content">
                <!-- Overview Section -->
                <section class="content-section active" id="overview">
                    <h2>Overview & Core Concepts</h2>
                    <div class="section-intro">
                        <p>Software Architecture Recovery và Reverse Engineering Documentation là lĩnh vực chuyên môn cao trong kỹ thuật phần mềm, đòi hỏi sự kết hợp giữa Static Code Analysis, Linux System Administration, Graph Theory và Documentation Automation.</p>
                    </div>
                    
                    <div class="card">
                        <div class="card__body">
                            <h3>Kỹ năng cốt lõi</h3>
                            <ul class="skill-list">
                                <li><strong>Static Code Analysis</strong> - Phân tích tĩnh mã nguồn</li>
                                <li><strong>Linux System Administration</strong> - Command-line mastery</li>
                                <li><strong>Graph Theory</strong> - Visualization techniques</li>
                                <li><strong>Documentation Automation</strong> - Tự động hóa tài liệu</li>
                            </ul>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card__body">
                            <h3>Mindset "Khảo cổ phần mềm"</h3>
                            <p>Chuyển đổi tư duy phát triển phần mềm truyền thống (code-first → design) thành một tư duy maps-first → develop dựa trên việc mô hình hóa và vẽ bản đồ phần mềm toàn diện.</p>
                        </div>
                    </div>
                </section>

                <!-- Linux Commands Section -->
                <section class="content-section" id="linux-commands">
                    <h2>Linux Commands & System Analysis</h2>
                    <div class="section-intro">
                        <p>Các lệnh Linux cơ bản để phân tích cấu trúc và dependency của dự án phần mềm.</p>
                    </div>

                    <div class="command-grid">
                        <div class="command-card">
                            <h3>Project Structure Analysis</h3>
                            <p>Tìm tất cả files trong project và phân tích cấu trúc</p>
                            <div class="code-block">
                                <pre><code class="language-bash">find /path/to/project -type f -name "*.py" -o -name "*.js" -o -name "*.java" | wc -l</code></pre>
                                <button class="copy-btn" data-clipboard-text='find /path/to/project -type f -name "*.py" -o -name "*.js" -o -name "*.java" | wc -l'>📋</button>
                            </div>
                        </div>

                        <div class="command-card">
                            <h3>Dependency Analysis</h3>
                            <p>Phân tích dependency bằng grep và xargs</p>
                            <div class="code-block">
                                <pre><code class="language-bash">find . -name "*.py" | xargs grep -l "import\|from" | \
    xargs grep -H "import\|from" > dependencies_raw.txt</code></pre>
                                <button class="copy-btn" data-clipboard-text='find . -name "*.py" | xargs grep -l "import\|from" | \
    xargs grep -H "import\|from" > dependencies_raw.txt'>📋</button>
                            </div>
                        </div>

                        <div class="command-card">
                            <h3>Database Connection Analysis</h3>
                            <p>Tìm tất cả database connections</p>
                            <div class="code-block">
                                <pre><code class="language-bash">find . -type f \( -name "*.py" -o -name "*.js" \) | \
    xargs grep -l "connect\|query\|SELECT\|INSERT" > db_files.txt</code></pre>
                                <button class="copy-btn" data-clipboard-text='find . -type f \( -name "*.py" -o -name "*.js" \) | \
    xargs grep -l "connect\|query\|SELECT\|INSERT" > db_files.txt'>📋</button>
                            </div>
                        </div>

                        <div class="command-card">
                            <h3>Function Mapping</h3>
                            <p>Phân tích function calls và relationships</p>
                            <div class="code-block">
                                <pre><code class="language-bash">find . -name "*.py" | xargs grep -n "def " | \
    sed 's/:def /|/' > functions_map.txt</code></pre>
                                <button class="copy-btn" data-clipboard-text='find . -name "*.py" | xargs grep -n "def " | \
    sed '"'"'s/:def /|/'"'"' > functions_map.txt'>📋</button>
                            </div>
                        </div>

                        <div class="command-card">
                            <h3>Dependency Matrix Creation</h3>
                            <p>Tạo dependency matrix từ source code</p>
                            <div class="code-block">
                                <pre><code class="language-bash">for file in $(find . -name "*.py"); do
  echo "=== $file ==="
  grep -n "import\|from" "$file" | \
    sed "s|^|$file:|"
done > full_dependency_map.txt</code></pre>
                                <button class="copy-btn" data-clipboard-text='for file in $(find . -name "*.py"); do
  echo "=== $file ==="
  grep -n "import\|from" "$file" | \
    sed "s|^|$file:|"
done > full_dependency_map.txt'>📋</button>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Static Analysis Section -->
                <section class="content-section" id="static-analysis">
                    <h2>Static Code Analysis Tools</h2>
                    
                    <div class="tool-grid">
                        <div class="tool-card">
                            <h3>Doxygen</h3>
                            <p class="tool-description">Tạo documentation từ comments</p>
                            <div class="tool-details">
                                <h4>Installation:</h4>
                                <div class="code-block">
                                    <pre><code class="language-bash">sudo apt-get install doxygen graphviz</code></pre>
                                    <button class="copy-btn" data-clipboard-text="sudo apt-get install doxygen graphviz">📋</button>
                                </div>
                                <h4>Usage:</h4>
                                <div class="code-block">
                                    <pre><code class="language-bash">doxygen -g config_file
sed -i 's/EXTRACT_ALL.*=.*NO/EXTRACT_ALL = YES/' config_file
sed -i 's/HAVE_DOT.*=.*NO/HAVE_DOT = YES/' config_file
doxygen config_file</code></pre>
                                    <button class="copy-btn" data-clipboard-text="doxygen -g config_file
sed -i 's/EXTRACT_ALL.*=.*NO/EXTRACT_ALL = YES/' config_file
sed -i 's/HAVE_DOT.*=.*NO/HAVE_DOT = YES/' config_file
doxygen config_file">📋</button>
                                </div>
                            </div>
                        </div>

                        <div class="tool-card">
                            <h3>Madge</h3>
                            <p class="tool-description">JavaScript dependency analysis</p>
                            <div class="tool-details">
                                <h4>Installation:</h4>
                                <div class="code-block">
                                    <pre><code class="language-bash">npm install -g madge</code></pre>
                                    <button class="copy-btn" data-clipboard-text="npm install -g madge">📋</button>
                                </div>
                                <h4>Usage:</h4>
                                <div class="code-block">
                                    <pre><code class="language-bash">madge --format es6 --image graph.svg src/</code></pre>
                                    <button class="copy-btn" data-clipboard-text="madge --format es6 --image graph.svg src/">📋</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Database Analysis Section -->
                <section class="content-section" id="database-analysis">
                    <h2>Database Schema Analysis</h2>
                    
                    <div class="technique-grid">
                        <div class="technique-card">
                            <h3>MySQL Schema Export</h3>
                            <p>Extract database schema từ MySQL</p>
                            <div class="code-block">
                                <pre><code class="language-bash">mysqldump -u user -p --no-data --routines database_name > schema.sql</code></pre>
                                <button class="copy-btn" data-clipboard-text="mysqldump -u user -p --no-data --routines database_name > schema.sql">📋</button>
                            </div>
                        </div>

                        <div class="technique-card">
                            <h3>Table Relationships Analysis</h3>
                            <p>Analyze tables and relationships</p>
                            <div class="code-block">
                                <pre><code class="language-bash">grep -E "(CREATE TABLE|FOREIGN KEY)" schema.sql | \
    sed 's/.*`\([^`]*\)`.*/\1/' > tables_relationships.txt</code></pre>
                                <button class="copy-btn" data-clipboard-text='grep -E "(CREATE TABLE|FOREIGN KEY)" schema.sql | \
    sed '"'"'s/.*`\([^`]*\)`.*/\1/'"'"' > tables_relationships.txt'>📋</button>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Documentation Generation Section -->
                <section class="content-section" id="documentation-generation">
                    <h2>Automated Documentation Generation</h2>
                    
                    <div class="card">
                        <div class="card__body">
                            <h3>Python Analyzer Script</h3>
                            <p>Script Python để tự động tạo documentation</p>
                            <div class="code-block large">
                                <pre><code class="language-python">import os
import ast
import json

def analyze_python_file(filepath):
    with open(filepath, 'r') as f:
        tree = ast.parse(f.read())
    
    analysis = {
        'classes': [],
        'functions': [],
        'imports': [],
        'docstrings': []
    }
    
    for node in ast.walk(tree):
        if isinstance(node, ast.ClassDef):
            analysis['classes'].append({
                'name': node.name,
                'lineno': node.lineno,
                'methods': [m.name for m in node.body if isinstance(m, ast.FunctionDef)]
            })
        elif isinstance(node, ast.FunctionDef):
            analysis['functions'].append({
                'name': node.name,
                'lineno': node.lineno,
                'args': [arg.arg for arg in node.args.args]
            })
    
    return analysis

# Analyze entire project
project_analysis = {}
for root, dirs, files in os.walk('.'):
    for file in files:
        if file.endswith('.py'):
            filepath = os.path.join(root, file)
            project_analysis[filepath] = analyze_python_file(filepath)

# Export to JSON for further processing
with open('project_analysis.json', 'w') as f:
    json.dump(project_analysis, f, indent=2)</code></pre>
                                <button class="copy-btn" data-clipboard-text="import os
import ast
import json

def analyze_python_file(filepath):
    with open(filepath, 'r') as f:
        tree = ast.parse(f.read())
    
    analysis = {
        'classes': [],
        'functions': [],
        'imports': [],
        'docstrings': []
    }
    
    for node in ast.walk(tree):
        if isinstance(node, ast.ClassDef):
            analysis['classes'].append({
                'name': node.name,
                'lineno': node.lineno,
                'methods': [m.name for m in node.body if isinstance(m, ast.FunctionDef)]
            })
        elif isinstance(node, ast.FunctionDef):
            analysis['functions'].append({
                'name': node.name,
                'lineno': node.lineno,
                'args': [arg.arg for arg in node.args.args]
            })
    
    return analysis

# Analyze entire project
project_analysis = {}
for root, dirs, files in os.walk('.'):
    for file in files:
        if file.endswith('.py'):
            filepath = os.path.join(root, file)
            project_analysis[filepath] = analyze_python_file(filepath)

# Export to JSON for further processing
with open('project_analysis.json', 'w') as f:
    json.dump(project_analysis, f, indent=2)">📋</button>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Graph Generation Section -->
                <section class="content-section" id="graph-generation">
                    <h2>Graph Generation & Visualization</h2>
                    
                    <div class="card">
                        <div class="card__body">
                            <h3>GraphViz Dependency Script</h3>
                            <div class="code-block large">
                                <pre><code class="language-python">import os
import re
import sys

print("digraph dependencies {")
print("  rankdir=LR;")
print("  node [shape=box];")

# Analyze Python imports
for root, dirs, files in os.walk('.'):
    for file in files:
        if file.endswith('.py'):
            filepath = os.path.join(root, file)
            filename = file[:-3]  # Remove .py
            
            with open(filepath, 'r') as f:
                content = f.read()
                
            # Find imports
            imports = re.findall(r'from\s+(\w+)\s+import|import\s+(\w+)', content)
            for imp in imports:
                imported = imp[0] if imp[0] else imp[1]
                if not imported.startswith('_'):
                    print(f'  "{filename}" -> "{imported}";')

print("}")</code></pre>
                                <button class="copy-btn" data-clipboard-text='import os
import re
import sys

print("digraph dependencies {")
print("  rankdir=LR;")
print("  node [shape=box];")

# Analyze Python imports
for root, dirs, files in os.walk("."):
    for file in files:
        if file.endswith(".py"):
            filepath = os.path.join(root, file)
            filename = file[:-3]  # Remove .py
            
            with open(filepath, "r") as f:
                content = f.read()
                
            # Find imports
            imports = re.findall(r"from\s+(\w+)\s+import|import\s+(\w+)", content)
            for imp in imports:
                imported = imp[0] if imp[0] else imp[1]
                if not imported.startswith("_"):
                    print(f'"  {filename}" -> "{imported}";")

print("}")'>📋</button>
                            </div>
                            <div class="usage-steps">
                                <h4>Usage:</h4>
                                <div class="code-block">
                                    <pre><code class="language-bash">python3 generate_graph.py > project_deps.dot
dot -Tpng project_deps.dot -o dependency_graph.png</code></pre>
                                    <button class="copy-btn" data-clipboard-text="python3 generate_graph.py > project_deps.dot
dot -Tpng project_deps.dot -o dependency_graph.png">📋</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Comprehensive Script Section -->
                <section class="content-section" id="comprehensive-script">
                    <h2>Comprehensive Analysis Script</h2>
                    
                    <div class="card">
                        <div class="card__body">
                            <h3>Complete Reverse Engineering Script</h3>
                            <div class="code-block large">
                                <pre><code class="language-bash">#!/bin/bash
# comprehensive_analysis.sh - Complete reverse engineering script

PROJECT_PATH=${1:-.}
OUTPUT_DIR="analysis_output"

echo "Starting comprehensive source code analysis..."
mkdir -p $OUTPUT_DIR

# 1. Project structure analysis
echo "Analyzing project structure..."
find $PROJECT_PATH -type f | grep -E '\.(py|js|java|php|rb)$' > $OUTPUT_DIR/source_files.txt
tree $PROJECT_PATH -I '__pycache__|node_modules|.git' > $OUTPUT_DIR/project_tree.txt

# 2. Dependency analysis
echo "Extracting dependencies..."
find $PROJECT_PATH -name "*.py" | xargs grep -h "import\|from" | \
    sort | uniq > $OUTPUT_DIR/dependencies.txt

# 3. Database schema extraction
echo "Analyzing database connections..."
find $PROJECT_PATH -type f | xargs grep -l "CREATE TABLE\|SELECT\|INSERT\|UPDATE" | \
    head -10 > $OUTPUT_DIR/db_related_files.txt

# 4. Function and class mapping
echo "Mapping functions and classes..."
find $PROJECT_PATH -name "*.py" | xargs grep -n "^class\|^def " > $OUTPUT_DIR/functions_classes.txt

# 5. Configuration files
echo "Finding configuration files..."
find $PROJECT_PATH -name "*.conf" -o -name "*.ini" -o -name "*.yaml" -o -name "*.json" > $OUTPUT_DIR/config_files.txt</code></pre>
                                <button class="copy-btn" data-clipboard-text='#!/bin/bash
# comprehensive_analysis.sh - Complete reverse engineering script

PROJECT_PATH=${1:-.}
OUTPUT_DIR="analysis_output"

echo "Starting comprehensive source code analysis..."
mkdir -p $OUTPUT_DIR

# 1. Project structure analysis
echo "Analyzing project structure..."
find $PROJECT_PATH -type f | grep -E "\.(py|js|java|php|rb)$" > $OUTPUT_DIR/source_files.txt
tree $PROJECT_PATH -I "__pycache__|node_modules|.git" > $OUTPUT_DIR/project_tree.txt

# 2. Dependency analysis
echo "Extracting dependencies..."
find $PROJECT_PATH -name "*.py" | xargs grep -h "import\|from" | \
    sort | uniq > $OUTPUT_DIR/dependencies.txt

# 3. Database schema extraction
echo "Analyzing database connections..."
find $PROJECT_PATH -type f | xargs grep -l "CREATE TABLE\|SELECT\|INSERT\|UPDATE" | \
    head -10 > $OUTPUT_DIR/db_related_files.txt

# 4. Function and class mapping
echo "Mapping functions and classes..."
find $PROJECT_PATH -name "*.py" | xargs grep -n "^class\|^def " > $OUTPUT_DIR/functions_classes.txt

# 5. Configuration files
echo "Finding configuration files..."
find $PROJECT_PATH -name "*.conf" -o -name "*.ini" -o -name "*.yaml" -o -name "*.json" > $OUTPUT_DIR/config_files.txt'>📋</button>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Professional Tools Section -->
                <section class="content-section" id="professional-tools">
                    <h2>Professional Tools</h2>
                    
                    <div class="tools-section">
                        <h3>Enterprise Tools</h3>
                        <div class="tools-grid">
                            <div class="tool-item">
                                <h4>Imagix 4D</h4>
                                <p>Professional source code analysis</p>
                                <span class="status status--warning">Commercial</span>
                            </div>
                            <div class="tool-item">
                                <h4>Understand</h4>
                                <p>Code analysis và reverse engineering</p>
                                <span class="status status--warning">Commercial</span>
                            </div>
                            <div class="tool-item">
                                <h4>SourceTrail</h4>
                                <p>Interactive source explorer</p>
                                <span class="status status--success">Open Source</span>
                            </div>
                            <div class="tool-item">
                                <h4>Axivion</h4>
                                <p>Architecture verification</p>
                                <span class="status status--warning">Commercial</span>
                            </div>
                        </div>
                    </div>

                    <div class="tools-section">
                        <h3>Open Source Tools</h3>
                        <div class="tools-grid">
                            <div class="tool-item">
                                <h4>Sourcegraph</h4>
                                <p>Code search và navigation</p>
                                <span class="status status--success">Open Source</span>
                            </div>
                            <div class="tool-item">
                                <h4>Neo4j</h4>
                                <p>Graph database để lưu relationships</p>
                                <span class="status status--success">Open Source</span>
                            </div>
                            <div class="tool-item">
                                <h4>PlantUML</h4>
                                <p>Generate UML từ code</p>
                                <span class="status status--success">Open Source</span>
                            </div>
                            <div class="tool-item">
                                <h4>Arcan</h4>
                                <p>Dependency analysis for Java</p>
                                <span class="status status--success">Open Source</span>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Workflows Section -->
                <section class="content-section" id="workflows">
                    <h2>Workflows & Best Practices</h2>
                    
                    <div class="card">
                        <div class="card__body">
                            <h3>Optimal Workflow</h3>
                            <div class="workflow-steps">
                                <div class="workflow-step">
                                    <h4>1. Discovery Phase</h4>
                                    <div class="code-block">
                                        <pre><code class="language-bash">./comprehensive_analysis.sh /path/to/project</code></pre>
                                        <button class="copy-btn" data-clipboard-text="./comprehensive_analysis.sh /path/to/project">📋</button>
                                    </div>
                                </div>
                                <div class="workflow-step">
                                    <h4>2. Documentation Phase</h4>
                                    <div class="code-block">
                                        <pre><code class="language-bash">doxygen config_file
sphinx-build -b html source build</code></pre>
                                        <button class="copy-btn" data-clipboard-text="doxygen config_file
sphinx-build -b html source build">📋</button>
                                    </div>
                                </div>
                                <div class="workflow-step">
                                    <h4>3. Graph Generation</h4>
                                    <div class="code-block">
                                        <pre><code class="language-bash">madge --image graph.svg src/
dot -Tpng dependencies.dot -o arch_diagram.png</code></pre>
                                        <button class="copy-btn" data-clipboard-text="madge --image graph.svg src/
dot -Tpng dependencies.dot -o arch_diagram.png">📋</button>
                                    </div>
                                </div>
                                <div class="workflow-step">
                                    <h4>4. Final Integration</h4>
                                    <div class="code-block">
                                        <pre><code class="language-bash">pandoc analysis_report.md -o final_documentation.pdf</code></pre>
                                        <button class="copy-btn" data-clipboard-text="pandoc analysis_report.md -o final_documentation.pdf">📋</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card__body">
                            <h3>Ứng dụng thực tế</h3>
                            <ul class="application-list">
                                <li>Legacy system modernization</li>
                                <li>Technical debt assessment</li>
                                <li>Code review và architecture validation</li>
                                <li>Onboarding new team members</li>
                                <li>Compliance và audit requirements</li>
                            </ul>
                        </div>
                    </div>
                </section>
            </main>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-bash.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-python.min.js"></script>
    <script src="app.js"></script>
</body>
</html>