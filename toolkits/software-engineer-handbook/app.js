// Toolkit Handbook - Interactive JavaScript (Fixed)

class ToolkitHandbook {
    constructor() {
        this.currentSection = 'home';
        this.currentTopic = '';
        this.currentView = 'overview';
        this.bookmarks = new Set();
        this.progress = {};
        this.searchIndex = [];
        this.isDarkMode = false;
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeTheme();
        this.buildSearchIndex();
        this.updateProgress();
        this.loadBookmarks();
        this.loadProgress();
    }

    setupEventListeners() {
        // Theme toggle
        const themeToggle = document.getElementById('themeToggle');
        themeToggle.addEventListener('click', () => this.toggleTheme());

        // Logo navigation
        const headerTitle = document.querySelector('.header-title');
        headerTitle.addEventListener('click', () => this.showHome());
        headerTitle.style.cursor = 'pointer';

        // Search functionality
        const searchInput = document.getElementById('searchInput');
        const searchBtn = document.querySelector('.search-btn');
        
        searchInput.addEventListener('input', (e) => this.handleSearch(e.target.value));
        searchInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                this.handleSearch(e.target.value);
            }
        });
        searchBtn.addEventListener('click', () => this.handleSearch(searchInput.value));

        // Navigation
        document.querySelectorAll('.nav-header').forEach(header => {
            header.addEventListener('click', (e) => this.toggleNavSection(e.currentTarget));
        });

        document.querySelectorAll('.nav-subitem').forEach(item => {
            item.addEventListener('click', (e) => this.navigateToTopic(e.currentTarget));
        });

        document.querySelectorAll('.overview-card').forEach(card => {
            card.addEventListener('click', (e) => this.navigateToSection(e.currentTarget));
        });

        // View tabs
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.switchView(e.currentTarget));
        });

        // Bookmark button
        const bookmarkBtn = document.getElementById('bookmarkBtn');
        if (bookmarkBtn) {
            bookmarkBtn.addEventListener('click', () => this.toggleBookmark());
        }

        // Modal functionality
        const modal = document.getElementById('codeModal');
        const closeBtn = modal.querySelector('.modal-close');
        closeBtn.addEventListener('click', () => this.closeModal());
        
        modal.addEventListener('click', (e) => {
            if (e.target === modal) this.closeModal();
        });

        // Copy functionality
        const copyBtn = document.getElementById('copyBtn');
        copyBtn.addEventListener('click', () => this.copyCode());

        // Mobile navigation
        this.setupMobileNav();

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => this.handleKeyboardShortcuts(e));
    }

    initializeTheme() {
        const savedTheme = localStorage.getItem('unifiedToolkit.theme') || 'light';
        this.setTheme(savedTheme);
        
        // Listen for theme changes from parent (unified toolkit)
        window.addEventListener('message', (event) => {
            if (event.data && event.data.type === 'THEME_CHANGE') {
                this.setTheme(event.data.theme);
            }
        });
    }

    toggleTheme() {
        const newTheme = this.isDarkMode ? 'light' : 'dark';
        this.setTheme(newTheme);
        
        // Broadcast theme change to parent (unified toolkit)
        if (window.parent !== window) {
            try {
                window.parent.postMessage({
                    type: 'THEME_CHANGE',
                    theme: newTheme
                }, '*');
            } catch (e) {
                // Ignore cross-origin errors
            }
        }
    }

    setTheme(theme) {
        this.isDarkMode = theme === 'dark';
        document.documentElement.setAttribute('data-color-scheme', theme);
        
        const themeToggle = document.getElementById('themeToggle');
        themeToggle.textContent = this.isDarkMode ? '☀️' : '🌙';
        
        localStorage.setItem('unifiedToolkit.theme', theme);
    }

    buildSearchIndex() {
        this.searchIndex = [
            // Enterprise Platform Architecture
            { section: 'enterprise_platform', topic: 'system-design', title: 'System Design', content: 'scalability load balancing caching database design microservices architecture' },
            { section: 'enterprise_platform', topic: 'clean-architecture', title: 'Clean Architecture + DDD', content: 'domain driven design clean architecture dependency inversion entities use cases' },
            { section: 'enterprise_platform', topic: 'microservices', title: 'Microservices', content: 'microservices service mesh api gateway distributed systems docker kubernetes' },
            { section: 'enterprise_platform', topic: 'ai-native', title: 'AI-Native Design', content: 'machine learning integration ai pipelines model deployment feature stores' },
            
            // Programming Languages
            { section: 'programming_languages', topic: 'javascript', title: 'JavaScript/TypeScript', content: 'javascript typescript nodejs react async await promises closures' },
            { section: 'programming_languages', topic: 'python', title: 'Python', content: 'python django flask pandas numpy machine learning data science' },
            { section: 'programming_languages', topic: 'go', title: 'Go', content: 'golang goroutines channels concurrency performance web servers' },
            { section: 'programming_languages', topic: 'cpp-rust', title: 'C++/Rust', content: 'c++ rust memory management performance system programming safety' },
            
            // OOP & Design Patterns
            { section: 'oop_patterns', topic: 'solid', title: 'SOLID Principles', content: 'single responsibility open closed liskov substitution interface segregation dependency inversion' },
            { section: 'oop_patterns', topic: 'gof', title: 'Gang of Four Patterns', content: 'singleton factory observer strategy adapter decorator facade' },
            
            // Data Structures & Algorithms
            { section: 'data_structures', topic: 'linear', title: 'Linear Structures', content: 'array linked list stack queue deque' },
            { section: 'data_structures', topic: 'nonlinear', title: 'Non-Linear Structures', content: 'tree binary tree graph hash table heap' },
            { section: 'data_structures', topic: 'algorithms', title: 'Algorithms', content: 'sorting searching dynamic programming greedy divide and conquer' },
            { section: 'data_structures', topic: 'complexity', title: 'Complexity Analysis', content: 'big o notation time complexity space complexity asymptotic analysis' },
        ];
    }

    handleSearch(query) {
        if (!query.trim()) {
            this.clearSearch();
            return;
        }

        const results = this.searchIndex.filter(item => 
            item.title.toLowerCase().includes(query.toLowerCase()) ||
            item.content.toLowerCase().includes(query.toLowerCase())
        );

        this.displaySearchResults(results, query);
    }

    displaySearchResults(results, query) {
        if (results.length === 0) {
            this.showNotification('Không tìm thấy kết quả nào 🔍');
            return;
        }

        // Show search results section
        this.currentSection = 'search';
        
        // Hide home, show section content
        document.getElementById('home').classList.remove('active');
        document.getElementById('section-content').classList.add('active');
        
        // Update title and breadcrumb
        const title = document.getElementById('section-title');
        title.textContent = `🔍 Kết quả tìm kiếm: "${query}"`;
        
        const breadcrumb = document.getElementById('breadcrumb');
        breadcrumb.innerHTML = `
            <span class="breadcrumb-item"><a href="#" onclick="app.showHome()">🏠 Trang chủ</a></span>
            <span class="breadcrumb-item active">🔍 Tìm kiếm</span>
        `;
        
        // Generate search results HTML
        const content = document.getElementById('dynamic-content');
        content.innerHTML = `
            <div class="search-results">
                <p>Tìm thấy ${results.length} kết quả cho "<strong>${query}</strong>"</p>
                <div class="content-grid">
                    ${results.map(result => `
                        <div class="content-card search-result-card" onclick="app.navigateToTopicFromSearch('${result.section}', '${result.topic}')">
                            <h3>${result.title}</h3>
                            <p>${this.highlightSearchTerm(result.content, query)}</p>
                            <div class="search-meta">
                                <span class="section-badge">${this.getSectionData(result.section).icon} ${this.getSectionData(result.section).title}</span>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
        
        this.showNotification(`Tìm thấy ${results.length} kết quả! 🎯`);
    }

    highlightSearchTerm(text, term) {
        const regex = new RegExp(`(${term})`, 'gi');
        return text.replace(regex, '<mark>$1</mark>');
    }

    navigateToTopicFromSearch(section, topic) {
        // Find and click the corresponding nav item
        const navSection = document.querySelector(`[data-section="${section}"]`);
        if (navSection && !navSection.classList.contains('expanded')) {
            navSection.classList.add('expanded');
        }
        
        const navItem = document.querySelector(`[data-topic="${topic}"]`);
        if (navItem) {
            navItem.click();
        }
    }

    clearSearch() {
        // Clear search results if showing
        if (this.currentSection === 'search') {
            this.showHome();
        }
    }

    toggleNavSection(header) {
        const navItem = header.parentElement;
        const isExpanded = navItem.classList.contains('expanded');
        
        // Close other sections
        document.querySelectorAll('.nav-item.expanded').forEach(item => {
            if (item !== navItem) item.classList.remove('expanded');
        });
        
        navItem.classList.toggle('expanded', !isExpanded);
    }

    navigateToSection(card) {
        const section = card.getAttribute('data-section');
        this.showSection(section);
    }

    navigateToTopic(item) {
        const topic = item.getAttribute('data-topic');
        const section = item.closest('.nav-item').getAttribute('data-section');
        
        // Update active states
        document.querySelectorAll('.nav-subitem.active').forEach(i => i.classList.remove('active'));
        item.classList.add('active');
        
        this.currentSection = section;
        this.currentTopic = topic;
        this.showSection(section, topic);
    }

    showSection(section, topic = null) {
        this.currentSection = section;
        this.currentTopic = topic || '';
        
        // Hide home, show section content
        document.getElementById('home').classList.remove('active');
        document.getElementById('section-content').classList.add('active');
        
        // Update breadcrumb
        this.updateBreadcrumb(section, topic);
        
        // Load section content
        this.loadSectionContent(section, topic);
        
        // Update progress
        this.markAsVisited(section, topic);
        this.updateProgress();
        
        // Reset view to overview
        this.currentView = 'overview';
        this.updateActiveTab();
    }

    updateBreadcrumb(section, topic = null) {
        const breadcrumb = document.getElementById('breadcrumb');
        const sectionData = this.getSectionData(section);
        
        let html = '<span class="breadcrumb-item"><a href="#" onclick="app.showHome()">🏠 Trang chủ</a></span>';
        html += `<span class="breadcrumb-item active">${sectionData.icon} ${sectionData.title}</span>`;
        
        if (topic) {
            const topicTitle = this.getTopicTitle(section, topic);
            html += `<span class="breadcrumb-item active">${topicTitle}</span>`;
        }
        
        breadcrumb.innerHTML = html;
    }

    loadSectionContent(section, topic = null) {
        const title = document.getElementById('section-title');
        const content = document.getElementById('dynamic-content');
        const sectionData = this.getSectionData(section);
        
        title.textContent = `${sectionData.icon} ${sectionData.title}`;
        
        if (topic) {
            this.loadTopicContent(section, topic, content);
        } else {
            this.loadSectionOverview(section, content);
        }
        
        // Add fade-in animation
        content.classList.add('fade-in');
        setTimeout(() => content.classList.remove('fade-in'), 300);
    }

    loadTopicContent(section, topic, container) {
        const contentData = this.getTopicContentData(section, topic);
        container.innerHTML = this.generateTopicHTML(contentData);
        this.setupContentInteractions(container);
        
        // Update bookmark button state
        this.updateBookmarkButton();
    }

    loadSectionOverview(section, container) {
        const sectionData = this.getSectionData(section);
        const overviewData = this.getSectionOverviewData(section);
        
        container.innerHTML = `
            <div class="content-grid">
                ${overviewData.map(item => `
                    <div class="content-card" onclick="app.navigateToTopicFromCard('${section}', '${item.id}')">
                        <h3>${item.title}</h3>
                        <p>${item.description}</p>
                        <div class="difficulty-badge">${item.difficulty}</div>
                    </div>
                `).join('')}
            </div>
        `;
    }

    updateBookmarkButton() {
        const key = `${this.currentSection}-${this.currentTopic}`;
        const btn = document.getElementById('bookmarkBtn');
        if (btn) {
            if (this.bookmarks.has(key)) {
                btn.classList.add('bookmarked');
            } else {
                btn.classList.remove('bookmarked');
            }
        }
    }

    updateActiveTab() {
        // Update active tab
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
            if (btn.getAttribute('data-view') === this.currentView) {
                btn.classList.add('active');
            }
        });
    }

    getSectionData(section) {
        const sections = {
            enterprise_platform: { title: 'Enterprise Platform Architecture', icon: '🏗️' },
            programming_languages: { title: 'Programming Languages', icon: '💻' },
            oop_patterns: { title: 'OOP & Design Patterns', icon: '🎨' },
            data_structures: { title: 'Data Structures & Algorithms', icon: '🔗' },
            database: { title: 'Database & SQL', icon: '💾' },
            devops: { title: 'DevOps & Cloud', icon: '☁️' },
            apis: { title: 'APIs & Communication', icon: '🌐' },
            ai_ml: { title: 'Machine Learning/AI', icon: '🤖' },
            soft_skills: { title: 'Thinking & Soft Skills', icon: '🧠' }
        };
        return sections[section] || { title: 'Unknown', icon: '❓' };
    }

    getSectionOverviewData(section) {
        const overviewData = {
            enterprise_platform: [
                { id: 'system-design', title: 'System Design', description: 'Scalability, Load Balancing, Caching', difficulty: 'Nâng cao' },
                { id: 'clean-architecture', title: 'Clean Architecture + DDD', description: 'Domain-Driven Design, Clean Code', difficulty: 'Nâng cao' },
                { id: 'microservices', title: 'Microservices', description: 'Distributed Systems, Service Mesh', difficulty: 'Nâng cao' },
                { id: 'ai-native', title: 'AI-Native Design', description: 'ML Integration, AI Pipelines', difficulty: 'Nâng cao' }
            ],
            programming_languages: [
                { id: 'javascript', title: 'JavaScript/TypeScript', description: 'Modern JS, Node.js, React, Async Programming', difficulty: 'Cơ bản' },
                { id: 'python', title: 'Python', description: 'Data Science, Web Development, ML Libraries', difficulty: 'Cơ bản' },
                { id: 'go', title: 'Go', description: 'Concurrency, Performance, Web Servers', difficulty: 'Trung cấp' },
                { id: 'cpp-rust', title: 'C++/Rust', description: 'System Programming, Memory Management', difficulty: 'Nâng cao' }
            ],
            oop_patterns: [
                { id: 'solid', title: 'SOLID Principles', description: 'Clean Code Design Principles', difficulty: 'Trung cấp' },
                { id: 'gof', title: 'Gang of Four Patterns', description: 'Classical Design Patterns', difficulty: 'Trung cấp' },
                { id: 'arch-patterns', title: 'Architectural Patterns', description: 'MVC, MVP, MVVM, Clean Architecture', difficulty: 'Nâng cao' },
                { id: 'best-practices', title: 'Best Practices', description: 'Code Quality, Refactoring', difficulty: 'Trung cấp' }
            ],
            data_structures: [
                { id: 'linear', title: 'Linear Structures', description: 'Array, Linked List, Stack, Queue', difficulty: 'Cơ bản' },
                { id: 'nonlinear', title: 'Non-Linear Structures', description: 'Tree, Graph, Hash Table, Heap', difficulty: 'Trung cấp' },
                { id: 'algorithms', title: 'Algorithms', description: 'Sorting, Searching, Dynamic Programming', difficulty: 'Trung cấp' },
                { id: 'complexity', title: 'Complexity Analysis', description: 'Big O Notation, Time/Space Complexity', difficulty: 'Cơ bản' }
            ]
        };
        
        return overviewData[section] || [];
    }

    getTopicContentData(section, topic) {
        // Comprehensive topic content data
        const contentDatabase = {
            'enterprise_platform': {
                'system-design': {
                    title: 'System Design',
                    overview: 'System Design là quá trình thiết kế kiến trúc, thành phần, giao diện và dữ liệu cho một hệ thống để đáp ứng các yêu cầu cụ thể.',
                    keyPoints: [
                        'Scalability (Khả năng mở rộng)',
                        'Reliability (Độ tin cậy)',
                        'Availability (Tính khả dụng)',
                        'Consistency (Tính nhất quán)',
                        'Performance (Hiệu suất)'
                    ],
                    details: {
                        'Scalability': 'Khả năng xử lý tải tăng thông qua horizontal scaling (thêm server) hoặc vertical scaling (nâng cấp hardware)',
                        'Load Balancing': 'Phân phối requests đều trên nhiều servers để tối ưu hiệu suất',
                        'Caching': 'Lưu trữ tạm thời dữ liệu để giảm thời gian truy cập (Redis, Memcached)',
                        'Database Design': 'Thiết kế schema, indexing, partitioning, replication',
                        'CAP Theorem': 'Chỉ có thể đảm bảo 2 trong 3: Consistency, Availability, Partition tolerance'
                    },
                    examples: {
                        'Load Balancer Config': `# Nginx Load Balancer Configuration
upstream backend {
    server ************:3000;
    server ************:3000;
    server ************:3000;
}

server {
    listen 80;
    server_name example.com;
    
    location / {
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}`,
                        'Caching Strategy': `// Redis Caching Example
const redis = require('redis');
const client = redis.createClient();

async function getCachedData(key) {
    try {
        const cached = await client.get(key);
        if (cached) {
            return JSON.parse(cached);
        }
        
        // Fetch from database
        const data = await fetchFromDatabase(key);
        
        // Cache for 1 hour
        await client.setex(key, 3600, JSON.stringify(data));
        
        return data;
    } catch (error) {
        console.error('Cache error:', error);
        return await fetchFromDatabase(key);
    }
}`
                    },
                    practice: [
                        'Thiết kế một hệ thống chat real-time cho 1 triệu users',
                        'Tạo kiến trúc cho một e-commerce platform',
                        'Thiết kế system để handle 100k requests/second'
                    ]
                },
                'clean-architecture': {
                    title: 'Clean Architecture + DDD',
                    overview: 'Clean Architecture kết hợp Domain-Driven Design tạo ra kiến trúc phần mềm dễ maintain, test và scale.',
                    keyPoints: [
                        'Domain Layer (Entities, Value Objects)',
                        'Application Layer (Use Cases)',
                        'Infrastructure Layer (Database, External APIs)',
                        'Presentation Layer (Controllers, Views)',
                        'Dependency Inversion Principle'
                    ],
                    details: {
                        'Entities': 'Business objects chứa logic core của domain',
                        'Use Cases': 'Application-specific business rules',
                        'Repository Pattern': 'Abstraction layer cho data access',
                        'Dependency Injection': 'Invert dependencies để dễ test',
                        'Bounded Context': 'Phân chia domain thành các context riêng biệt'
                    },
                    examples: {
                        'Entity Example': `// Domain Entity
class User {
    constructor(id, email, password) {
        this.id = id;
        this.email = email;
        this.password = password;
        this.createdAt = new Date();
    }
    
    changeEmail(newEmail) {
        if (!this.isValidEmail(newEmail)) {
            throw new Error('Invalid email');
        }
        this.email = newEmail;
    }
    
    isValidEmail(email) {
        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;
        return emailRegex.test(email);
    }
}`,
                        'Use Case Example': `// Application Use Case
class CreateUserUseCase {
    constructor(userRepository, emailService) {
        this.userRepository = userRepository;
        this.emailService = emailService;
    }
    
    async execute(userData) {
        // Business logic
        const user = new User(
            generateId(),
            userData.email,
            hashPassword(userData.password)
        );
        
        // Check business rules
        if (await this.userRepository.existsByEmail(user.email)) {
            throw new Error('Email already exists');
        }
        
        // Save user
        const savedUser = await this.userRepository.save(user);
        
        // Send welcome email
        await this.emailService.sendWelcomeEmail(savedUser);
        
        return savedUser;
    }
}`
                    }
                }
            },
            'programming_languages': {
                'javascript': {
                    title: 'JavaScript/TypeScript',
                    overview: 'JavaScript là ngôn ngữ lập trình dynamic, TypeScript thêm static typing để cải thiện developer experience.',
                    keyPoints: [
                        'ES6+ Features (Arrow functions, Destructuring, Modules)',
                        'Async Programming (Promises, async/await)',
                        'TypeScript Type System',
                        'Node.js Backend Development',
                        'React/Vue.js Frontend Development'
                    ],
                    details: {
                        'Closures': 'Functions có access đến variables từ outer scope',
                        'Prototypal Inheritance': 'Object inheritance thông qua prototype chain',
                        'Event Loop': 'Cơ chế xử lý asynchronous operations',
                        'Type Guards': 'TypeScript techniques để narrow types',
                        'Module System': 'ES6 modules, CommonJS, AMD'
                    },
                    examples: {
                        'Modern JavaScript': `// ES6+ Features
const users = [
    { name: 'John', age: 25 },
    { name: 'Jane', age: 30 },
    { name: 'Bob', age: 35 }
];

// Destructuring & Arrow Functions
const getAdults = (users) => 
    users.filter(({ age }) => age >= 18)
         .map(({ name, age }) => ({ name, age }));

// Async/Await
async function fetchUserData(userId) {
    try {
        const response = await fetch(\`/api/users/\${userId}\`);
        const userData = await response.json();
        return userData;
    } catch (error) {
        console.error('Failed to fetch user:', error);
        throw error;
    }
}`,
                        'TypeScript Advanced': `// Generic Utility Types
interface User {
    id: number;
    name: string;
    email: string;
    password: string;
}

// Pick specific properties
type PublicUser = Pick<User, 'id' | 'name' | 'email'>;

// Make all properties optional
type PartialUser = Partial<User>;

// Generic function with constraints
function processEntity<T extends { id: number }>(
    entity: T,
    processor: (item: T) => T
): T {
    console.log(\`Processing entity \${entity.id}\`);
    return processor(entity);
}

// Conditional types
type ApiResponse<T> = T extends string 
    ? { message: T }
    : { data: T };`
                    }
                }
            },
            'data_structures': {
                'linear': {
                    title: 'Linear Data Structures',
                    overview: 'Cấu trúc dữ liệu tuyến tính là những cấu trúc mà các phần tử được sắp xếp theo thứ tự tuần tự.',
                    keyPoints: [
                        'Array - Mảng tĩnh với random access',
                        'Linked List - Danh sách liên kết động',
                        'Stack - LIFO (Last In, First Out)',
                        'Queue - FIFO (First In, First Out)',
                        'Deque - Double-ended queue'
                    ],
                    details: {
                        'Array': 'Fixed-size, O(1) access, O(n) insertion/deletion',
                        'Linked List': 'Dynamic size, O(n) access, O(1) insertion/deletion tại node',
                        'Stack': 'push(), pop(), top(), useful cho recursion, undo operations',
                        'Queue': 'enqueue(), dequeue(), front(), useful cho BFS, task scheduling',
                        'Deque': 'Có thể thêm/xóa từ cả hai đầu'
                    },
                    examples: {
                        'Stack Implementation': `class Stack {
    constructor() {
        this.items = [];
    }
    
    push(item) {
        this.items.push(item);
    }
    
    pop() {
        if (this.isEmpty()) {
            throw new Error('Stack is empty');
        }
        return this.items.pop();
    }
    
    peek() {
        if (this.isEmpty()) {
            return null;
        }
        return this.items[this.items.length - 1];
    }
    
    isEmpty() {
        return this.items.length === 0;
    }
    
    size() {
        return this.items.length;
    }
}

// Usage example
const stack = new Stack();
stack.push(1);
stack.push(2);
stack.push(3);
console.log(stack.pop()); // 3
console.log(stack.peek()); // 2`,
                        'Linked List Implementation': `class ListNode {
    constructor(val, next = null) {
        this.val = val;
        this.next = next;
    }
}

class LinkedList {
    constructor() {
        this.head = null;
        this.size = 0;
    }
    
    prepend(val) {
        const newNode = new ListNode(val, this.head);
        this.head = newNode;
        this.size++;
    }
    
    append(val) {
        const newNode = new ListNode(val);
        
        if (!this.head) {
            this.head = newNode;
        } else {
            let current = this.head;
            while (current.next) {
                current = current.next;
            }
            current.next = newNode;
        }
        this.size++;
    }
    
    delete(val) {
        if (!this.head) return false;
        
        if (this.head.val === val) {
            this.head = this.head.next;
            this.size--;
            return true;
        }
        
        let current = this.head;
        while (current.next && current.next.val !== val) {
            current = current.next;
        }
        
        if (current.next) {
            current.next = current.next.next;
            this.size--;
            return true;
        }
        
        return false;
    }
}`
                    }
                }
            }
        };
        
        return contentDatabase[section]?.[topic] || {
            title: 'Content not found',
            overview: 'Nội dung đang được cập nhật...',
            keyPoints: [],
            details: {},
            examples: {},
            practice: []
        };
    }

    generateTopicHTML(contentData) {
        return `
            <div class="tab-content active" data-tab="overview">
                <h2>📖 Tổng quan</h2>
                <p>${contentData.overview}</p>
                
                <div class="highlight">
                    <h3>🎯 Điểm quan trọng:</h3>
                    <ul>
                        ${contentData.keyPoints.map(point => `<li>${point}</li>`).join('')}
                    </ul>
                </div>
            </div>
            
            <div class="tab-content" data-tab="details" style="display: none;">
                <h2>📚 Chi tiết</h2>
                ${Object.entries(contentData.details || {}).map(([key, value]) => `
                    <div class="info-box">
                        <h4>${key}</h4>
                        <p>${value}</p>
                    </div>
                `).join('')}
            </div>
            
            <div class="tab-content" data-tab="examples" style="display: none;">
                <h2>💻 Ví dụ Code</h2>
                ${Object.entries(contentData.examples || {}).map(([title, code]) => `
                    <div class="code-example">
                        <h4>${title}</h4>
                        <pre><code>${this.escapeHtml(code)}</code></pre>
                        <button class="code-copy-btn" onclick="app.copyToClipboard(\`${this.escapeForAttribute(code)}\`)">📋 Copy</button>
                    </div>
                `).join('')}
            </div>
            
            <div class="tab-content" data-tab="practice" style="display: none;">
                <h2>🎯 Thực hành</h2>
                ${(contentData.practice || []).map(item => `
                    <div class="warning-box">
                        <p><strong>Bài tập:</strong> ${item}</p>
                    </div>
                `).join('')}
                ${(contentData.practice || []).length === 0 ? '<p>Bài tập thực hành đang được cập nhật...</p>' : ''}
            </div>
        `;
    }

    setupContentInteractions(container) {
        // Setup copy buttons
        container.querySelectorAll('.code-copy-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
            });
        });
    }

    switchView(btn) {
        const view = btn.getAttribute('data-view');
        this.currentView = view;
        
        // Update active tab
        document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
        btn.classList.add('active');
        
        // Show corresponding content
        const container = document.getElementById('dynamic-content');
        container.querySelectorAll('.tab-content').forEach(content => {
            content.style.display = 'none';
        });
        
        const targetContent = container.querySelector(`[data-tab="${view}"]`);
        if (targetContent) {
            targetContent.style.display = 'block';
        }
    }

    toggleBookmark() {
        const key = `${this.currentSection}-${this.currentTopic}`;
        const btn = document.getElementById('bookmarkBtn');
        
        if (this.bookmarks.has(key)) {
            this.bookmarks.delete(key);
            btn.classList.remove('bookmarked');
            this.showNotification('Đã xóa bookmark! 📌');
        } else {
            this.bookmarks.add(key);
            btn.classList.add('bookmarked');
            this.showNotification('Đã thêm bookmark! ⭐');
        }
        
        this.saveBookmarks();
    }

    saveBookmarks() {
        localStorage.setItem('handbook-bookmarks', JSON.stringify([...this.bookmarks]));
    }

    loadBookmarks() {
        const saved = localStorage.getItem('handbook-bookmarks');
        if (saved) {
            this.bookmarks = new Set(JSON.parse(saved));
        }
    }

    markAsVisited(section, topic = '') {
        const key = topic ? `${section}-${topic}` : section;
        if (!this.progress[key]) {
            this.progress[key] = { visited: true, timestamp: Date.now() };
            this.saveProgress();
        }
    }

    updateProgress() {
        const totalItems = 36; // Approximate total topics
        const visitedItems = Object.keys(this.progress).length;
        const progressPercent = Math.round((visitedItems / totalItems) * 100);
        
        const progressText = document.querySelector('.progress-text');
        const progressFill = document.querySelector('.progress-fill');
        
        if (progressText) progressText.textContent = `Tiến độ: ${progressPercent}%`;
        if (progressFill) progressFill.style.width = `${progressPercent}%`;
    }

    saveProgress() {
        localStorage.setItem('handbook-progress', JSON.stringify(this.progress));
    }

    loadProgress() {
        const saved = localStorage.getItem('handbook-progress');
        if (saved) {
            this.progress = JSON.parse(saved);
        }
    }

    showHome() {
        this.currentSection = 'home';
        this.currentTopic = '';
        
        document.getElementById('section-content').classList.remove('active');
        document.getElementById('home').classList.add('active');
        
        // Update breadcrumb
        const breadcrumb = document.getElementById('breadcrumb');
        breadcrumb.innerHTML = '<span class="breadcrumb-item active">🏠 Trang chủ</span>';
        
        // Clear active navigation
        document.querySelectorAll('.nav-subitem.active').forEach(item => {
            item.classList.remove('active');
        });
        
        // Clear search input
        const searchInput = document.getElementById('searchInput');
        if (searchInput) searchInput.value = '';
    }

    navigateToTopicFromCard(section, topic) {
        // Find and expand the corresponding nav section
        const navSection = document.querySelector(`[data-section="${section}"]`);
        if (navSection && !navSection.classList.contains('expanded')) {
            navSection.classList.add('expanded');
        }
        
        // Find and click the corresponding nav item
        const navItem = document.querySelector(`[data-topic="${topic}"]`);
        if (navItem) {
            navItem.click();
        }
    }

    openModal(title, code) {
        const modal = document.getElementById('codeModal');
        const modalTitle = document.getElementById('modal-title');
        const modalCode = document.getElementById('modal-code');
        
        modalTitle.textContent = title;
        modalCode.textContent = code;
        
        modal.classList.remove('hidden');
        document.body.style.overflow = 'hidden';
    }

    closeModal() {
        const modal = document.getElementById('codeModal');
        modal.classList.add('hidden');
        document.body.style.overflow = 'auto';
    }

    copyCode() {
        const code = document.getElementById('modal-code').textContent;
        this.copyToClipboard(code);
    }

    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            this.showNotification('Đã copy vào clipboard! 📋');
        } catch (err) {
            console.error('Failed to copy:', err);
            // Fallback for older browsers
            const textarea = document.createElement('textarea');
            textarea.value = text;
            document.body.appendChild(textarea);
            textarea.select();
            document.execCommand('copy');
            document.body.removeChild(textarea);
            this.showNotification('Đã copy vào clipboard! 📋');
        }
    }

    showNotification(message) {
        const notification = document.createElement('div');
        notification.className = 'notification';
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--color-success);
            color: var(--color-btn-primary-text);
            padding: 12px 24px;
            border-radius: 8px;
            z-index: 2000;
            animation: slideIn 0.3s ease;
            box-shadow: var(--shadow-md);
        `;
        
        document.body.appendChild(notification);
        setTimeout(() => {
            notification.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }

    setupMobileNav() {
        // Mobile navigation toggle
        if (window.innerWidth <= 768) {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.createElement('div');
            overlay.className = 'mobile-overlay';
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                z-index: 150;
                display: none;
            `;
            document.body.appendChild(overlay);
            
            // Add mobile menu button to header
            const mobileMenuBtn = document.createElement('button');
            mobileMenuBtn.innerHTML = '☰';
            mobileMenuBtn.className = 'mobile-menu-btn';
            mobileMenuBtn.style.cssText = `
                display: block;
                background: var(--color-primary);
                color: var(--color-btn-primary-text);
                border: none;
                padding: 8px 12px;
                border-radius: 6px;
                font-size: 18px;
                cursor: pointer;
            `;
            
            const headerControls = document.querySelector('.header-controls');
            headerControls.insertBefore(mobileMenuBtn, headerControls.firstChild);
            
            mobileMenuBtn.addEventListener('click', () => {
                sidebar.classList.add('open');
                overlay.style.display = 'block';
            });
            
            overlay.addEventListener('click', () => {
                sidebar.classList.remove('open');
                overlay.style.display = 'none';
            });
        }
    }

    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + K for search
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            document.getElementById('searchInput').focus();
        }
        
        // Escape to close modal
        if (e.key === 'Escape') {
            this.closeModal();
        }
        
        // Ctrl/Cmd + B to toggle bookmark
        if ((e.ctrlKey || e.metaKey) && e.key === 'b' && this.currentSection !== 'home') {
            e.preventDefault();
            this.toggleBookmark();
        }
    }

    getTopicTitle(section, topic) {
        const titles = {
            'system-design': 'System Design',
            'clean-architecture': 'Clean Architecture + DDD',
            'microservices': 'Microservices',
            'ai-native': 'AI-Native Design',
            'javascript': 'JavaScript/TypeScript',
            'python': 'Python',
            'go': 'Go',
            'cpp-rust': 'C++/Rust',
            'solid': 'SOLID Principles',
            'gof': 'Gang of Four Patterns',
            'linear': 'Linear Structures',
            'nonlinear': 'Non-Linear Structures',
            'algorithms': 'Algorithms',
            'complexity': 'Complexity Analysis'
        };
        return titles[topic] || topic;
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    escapeForAttribute(text) {
        return text.replace(/`/g, '\\`').replace(/\$/g, '\\$');
    }
}

// Initialize the application
const app = new ToolkitHandbook();

// Export for global access
window.app = app;